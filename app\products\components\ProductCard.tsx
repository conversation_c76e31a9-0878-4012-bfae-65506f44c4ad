"use client"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight, LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import dynamic from 'next/dynamic'
import { LucideProps } from 'lucide-react'
import { lazy } from "react"




interface ProductFeature {
  text: string
  iconName: IconName
}

interface Product {
  slug: string
  name: string
  description: string
  iconName: IconName
  features: ProductFeature[]
  highlight?: string
}

interface ProductCardProps {
  product: Product
  index: number
}

type IconName = string;

const loadIcon = async (iconName: string) => {
  try {
    const mod = await import('lucide-react');
    const Icon = mod[iconName as keyof typeof mod] as LucideIcon;
    return Icon || null;
  } catch (error) {
    console.error(`Error loading icon: ${iconName}`, error);
    return null;
  }
};

interface IconWrapperProps extends LucideProps {
  iconName: string;
}

const IconWrapper = ({ iconName, ...props }: IconWrapperProps) => {
  const DynamicIcon = dynamic<LucideProps>(
    () => loadIcon(iconName).then(Icon => Icon ? Icon : () => null),
    { loading: () => <div className="w-6 h-6 animate-pulse bg-primary/10 rounded" /> }
  );
  return <DynamicIcon {...props} />;
};

export function ProductCard({ product, index }: ProductCardProps) {
  return (
    <div className="group relative h-full">
      <div
        className={cn(
          "card-modern p-6 h-full hover-glow transition-all duration-500 flex flex-col",
          product.highlight && "ring-2 ring-offset-2 shadow-lg"
        )}
        style={{
          borderColor: product.highlight ? 'rgb(59 130 246)' : 'rgba(59, 130, 246, 0.1)',
          ringColor: product.highlight ? 'rgba(59, 130, 246, 0.3)' : undefined
        }}
      >
        {/* 高亮标签 */}
        {product.highlight && (
          <div className="absolute -top-3 -right-3 px-3 py-1 text-xs font-medium text-white rounded-full animate-pulse" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))' }}>
            {product.highlight}
          </div>
        )}

        {/* 头部 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="p-3 rounded-xl group-hover:scale-110 transition-transform duration-300" style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05))' }}>
                <IconWrapper iconName={product.iconName} className="h-6 w-6" style={{ color: 'rgb(59 130 246)' }} />
              </div>
              <div className="absolute inset-0 rounded-xl blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.2)' }} />
            </div>
          </div>
          {product.category && (
            <span className="px-2 py-1 text-xs font-medium rounded-md bg-slate-100 text-slate-600">
              {product.category}
            </span>
          )}
        </div>

        {/* 标题和描述 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-gradient-modern transition-colors duration-300">
            {product.name}
          </h3>
          <p className="text-sm text-slate-600 leading-relaxed line-clamp-3">
            {product.description}
          </p>
        </div>

        {/* 特性列表 */}
        <ul className="space-y-3 mb-6 flex-grow">
          {product.features.slice(0, 4).map((feature, featureIndex) => (
            <li
              key={feature.text}
              className="flex items-center space-x-3 text-sm group/item"
            >
              <div className="p-1 rounded-md transition-colors duration-200" style={{ background: 'rgba(59, 130, 246, 0.08)' }}>
                <IconWrapper iconName={feature.iconName} className="h-3 w-3" style={{ color: 'rgb(59 130 246)' }} />
              </div>
              <span className="text-slate-600 group-hover/item:text-slate-800 transition-colors">
                {feature.text}
              </span>
            </li>
          ))}
        </ul>

        {/* 价格信息 */}
        {(product as any).price && (
          <div className="mb-4 p-3 rounded-lg bg-slate-50 border border-slate-100">
            <div className="text-xs text-slate-500 mb-1">起始价格</div>
            <div className="text-lg font-semibold" style={{ color: 'rgb(59 130 246)' }}>
              {(product as any).price}
            </div>
          </div>
        )}

        {/* 按钮 */}
        <Button
          asChild
          className={cn(
            "w-full mt-auto group/btn transition-all duration-300",
            product.highlight
              ? "btn-modern shadow-button-modern"
              : "border hover:bg-white hover:shadow-md hover-lift"
          )}
          style={{
            background: product.highlight
              ? 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))'
              : 'white',
            borderColor: product.highlight ? 'transparent' : 'rgba(59, 130, 246, 0.2)',
            color: product.highlight ? 'white' : 'rgb(59 130 246)'
          }}
        >
          <Link href={`/products/${product.slug}`}>
            了解更多
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
          </Link>
        </Button>

        {/* 底部装饰线 */}
        <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235))' }} />
      </div>
    </div>
  )
}
