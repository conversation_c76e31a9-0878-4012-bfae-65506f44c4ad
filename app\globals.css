@tailwind base;
@tailwind components;
@tailwind utilities;

/* 现代化字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 250, 250, 250;
  --background-end-rgb: 255, 255, 255;

  /* 现代化渐变背景 - 基于 rgb(59 130 246) 主题色 */
  --gradient-primary: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(37 99 235) 100%);
  --gradient-secondary: linear-gradient(135deg, rgb(37 99 235) 0%, rgb(59 130 246) 100%);
  --gradient-accent: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(14 165 233) 100%);
  --gradient-hero: linear-gradient(135deg, rgb(59 130 246) 0%, rgb(37 99 235) 50%, rgb(29 78 216) 100%);
  --gradient-card: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(37, 99, 235, 0.03) 100%);

  /* 现代化玻璃拟态效果 */
  --glass-bg: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(59, 130, 246, 0.15);
  --glass-shadow: 0 8px 32px 0 rgba(59, 130, 246, 0.12);

  /* 主题色变量 */
  --theme-primary: 59, 130, 246;
  --theme-primary-dark: 37, 99, 235;
  --theme-primary-light: 96, 165, 250;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 10, 10, 10;
    --background-end-rgb: 0, 0, 0;

    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(59, 130, 246, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(59, 130, 246, 0.15);
  }
}

@layer base {
  :root {
    /* 现代化色彩系统 - 基于Tailwind蓝紫色调 */
    --background: 0 0% 100%;
    --foreground: 224 71% 4%;
    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    /* 主色调 - 基于 rgb(59 130 246) */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;

    /* 次要色彩 - 现代化蓝色系 */
    --secondary: 217 32% 96%;
    --secondary-foreground: 217 11% 46%;
    --muted: 217 32% 96%;
    --muted-foreground: 217 11% 46%;
    --accent: 217 32% 96%;
    --accent-foreground: 217 91% 20%;

    /* 功能色彩 */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 217 20% 91%;
    --input: 217 20% 91%;
    --ring: 217 91% 60%;

    /* 图表色彩 - 基于主题色的和谐搭配 */
    --chart-1: 217 91% 60%;  /* 主题色 */
    --chart-2: 213 94% 68%;  /* 浅蓝 */
    --chart-3: 221 83% 53%;  /* 深蓝 */
    --chart-4: 200 98% 39%;  /* 天蓝 */
    --chart-5: 217 91% 75%;  /* 亮蓝 */

    /* 现代化圆角 */
    --radius: 0.75rem;
  }
  .dark {
    /* 深色模式 - 蓝紫色调优化 */
    --background: 224 71% 4%;
    --foreground: 210 40% 98%;
    --card: 224 71% 4%;
    --card-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 210 40% 98%;

    /* 深色模式主色调 - 基于主题色 */
    --primary: 217 91% 60%;
    --primary-foreground: 217 91% 10%;

    /* 深色模式次要色彩 */
    --secondary: 217 28% 17%;
    --secondary-foreground: 217 40% 98%;
    --muted: 217 28% 17%;
    --muted-foreground: 217 11% 65%;
    --accent: 217 28% 17%;
    --accent-foreground: 217 40% 98%;

    /* 深色模式功能色彩 */
    --destructive: 0 63% 31%;
    --destructive-foreground: 217 40% 98%;
    --border: 217 28% 17%;
    --input: 217 28% 17%;
    --ring: 217 91% 60%;

    /* 深色模式图表色彩 - 基于主题色 */
    --chart-1: 217 91% 60%;  /* 主题色 */
    --chart-2: 213 94% 68%;  /* 浅蓝 */
    --chart-3: 221 83% 53%;  /* 深蓝 */
    --chart-4: 200 98% 39%;  /* 天蓝 */
    --chart-5: 217 91% 75%;  /* 亮蓝 */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    scroll-behavior: smooth;
  }

  /* 现代化滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/30;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/50;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary/20 text-primary;
  }

  /* 焦点样式优化 */
  :focus-visible {
    @apply outline-none ring-2 ring-primary/50 ring-offset-2 ring-offset-background;
  }
}

/* 现代化工具类 */
@layer components {
  /* 玻璃拟态效果 */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  /* 现代化渐变背景 */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  .gradient-card {
    background: var(--gradient-card);
  }

  /* 现代化卡片样式 */
  .modern-card {
    @apply bg-card/50 backdrop-blur-xl border border-border/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  /* 现代化按钮样式 */
  .modern-button {
    @apply relative overflow-hidden rounded-xl px-6 py-3 font-medium transition-all duration-300;
    @apply bg-gradient-to-r from-primary to-primary/80 text-primary-foreground;
    @apply hover:shadow-lg hover:shadow-primary/25 hover:scale-105;
    @apply active:scale-95;
  }

  .modern-button::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300;
  }

  .modern-button:hover::before {
    @apply opacity-100;
  }

  /* 现代化输入框样式 */
  .modern-input {
    @apply bg-background/50 backdrop-blur-sm border border-border/50 rounded-xl px-4 py-3;
    @apply focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-300;
  }

  /* 现代化文本样式 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent;
  }

  /* 现代化动画 */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* 现代化网格背景 */
  .grid-bg {
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* 现代化微交互效果 */
  .hover-lift {
    @apply transition-all duration-300 ease-out hover:-translate-y-1 hover:shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 ease-out;
  }

  .hover-glow:hover {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
  }

  /* 现代化边框效果 */
  .border-gradient {
    position: relative;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(37, 99, 235, 0.1)) border-box;
    border: 1px solid transparent;
  }

  /* 现代化脉冲效果 */
  .pulse-ring {
    @apply relative;
  }

  .pulse-ring::before {
    content: '';
    @apply absolute inset-0 rounded-full border-2 border-current opacity-75 animate-ping;
  }

  /* 现代化渐变文字 */
  .text-gradient-modern {
    background: linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235), rgb(29 78 216));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 现代化卡片悬停效果 */
  .card-modern {
    @apply bg-white/60 backdrop-blur-xl border border-white/20 rounded-2xl shadow-lg transition-all duration-500;
    @apply hover:bg-white/80 hover:shadow-xl hover:shadow-blue-500/10 hover:-translate-y-1;
  }

  /* 现代化按钮悬停效果 */
  .btn-modern {
    @apply relative overflow-hidden rounded-xl px-6 py-3 font-medium transition-all duration-300;
    @apply bg-gradient-to-r from-blue-600 to-blue-700 text-white;
    @apply hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105;
    @apply active:scale-95;
  }

  .btn-modern::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300;
  }

  .btn-modern:hover::before {
    @apply opacity-100;
  }
}

/* 现代化动画关键帧 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 现代化动画关键帧 */
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes rotate-in {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes pulse-glow-modern {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}
