@tailwind base;
@tailwind components;
@tailwind utilities;

/* 现代化字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 250, 250, 250;
  --background-end-rgb: 255, 255, 255;

  /* 现代化渐变背景 - 蓝紫色系优化 */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --gradient-hero: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
  --gradient-card: linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%);

  /* 玻璃拟态效果 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 10, 10, 10;
    --background-end-rgb: 0, 0, 0;

    --glass-bg: rgba(0, 0, 0, 0.1);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  }
}

@layer base {
  :root {
    /* 现代化色彩系统 - 基于Tailwind蓝紫色调 */
    --background: 0 0% 100%;
    --foreground: 224 71% 4%;
    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    /* 主色调 - Tailwind Indigo-600 优化 */
    --primary: 238 75% 65%;
    --primary-foreground: 0 0% 98%;

    /* 次要色彩 - 基于蓝色系 */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 9% 46%;
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;
    --accent: 220 14% 96%;
    --accent-foreground: 224 71% 4%;

    /* 功能色彩 */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 238 75% 65%;

    /* 图表色彩 - 蓝紫色系和谐搭配 */
    --chart-1: 238 75% 65%;  /* 主蓝紫 */
    --chart-2: 221 83% 53%;  /* 深蓝 */
    --chart-3: 262 83% 58%;  /* 紫色 */
    --chart-4: 200 98% 39%;  /* 青蓝 */
    --chart-5: 271 91% 65%;  /* 亮紫 */

    /* 现代化圆角 */
    --radius: 0.75rem;
  }
  .dark {
    /* 深色模式 - 蓝紫色调优化 */
    --background: 224 71% 4%;
    --foreground: 210 40% 98%;
    --card: 224 71% 4%;
    --card-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 210 40% 98%;

    /* 深色模式主色调 - 更亮的蓝紫色 */
    --primary: 238 75% 65%;
    --primary-foreground: 224 71% 4%;

    /* 深色模式次要色彩 */
    --secondary: 215 28% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 215 28% 17%;
    --muted-foreground: 217 11% 65%;
    --accent: 215 28% 17%;
    --accent-foreground: 210 40% 98%;

    /* 深色模式功能色彩 */
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 238 75% 65%;

    /* 深色模式图表色彩 - 蓝紫色系 */
    --chart-1: 238 75% 65%;  /* 主蓝紫 */
    --chart-2: 221 83% 53%;  /* 深蓝 */
    --chart-3: 262 83% 58%;  /* 紫色 */
    --chart-4: 200 98% 39%;  /* 青蓝 */
    --chart-5: 271 91% 65%;  /* 亮紫 */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    scroll-behavior: smooth;
  }

  /* 现代化滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/30;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/50;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary/20 text-primary;
  }

  /* 焦点样式优化 */
  :focus-visible {
    @apply outline-none ring-2 ring-primary/50 ring-offset-2 ring-offset-background;
  }
}

/* 现代化工具类 */
@layer components {
  /* 玻璃拟态效果 */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  /* 现代化渐变背景 */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  .gradient-card {
    background: var(--gradient-card);
  }

  /* 现代化卡片样式 */
  .modern-card {
    @apply bg-card/50 backdrop-blur-xl border border-border/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }

  /* 现代化按钮样式 */
  .modern-button {
    @apply relative overflow-hidden rounded-xl px-6 py-3 font-medium transition-all duration-300;
    @apply bg-gradient-to-r from-primary to-primary/80 text-primary-foreground;
    @apply hover:shadow-lg hover:shadow-primary/25 hover:scale-105;
    @apply active:scale-95;
  }

  .modern-button::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300;
  }

  .modern-button:hover::before {
    @apply opacity-100;
  }

  /* 现代化输入框样式 */
  .modern-input {
    @apply bg-background/50 backdrop-blur-sm border border-border/50 rounded-xl px-4 py-3;
    @apply focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-300;
  }

  /* 现代化文本样式 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary via-primary/80 to-primary/60 bg-clip-text text-transparent;
  }

  /* 现代化动画 */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* 现代化网格背景 */
  .grid-bg {
    background-image:
      linear-gradient(rgba(var(--primary), 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(var(--primary), 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }
}

/* 现代化动画关键帧 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(var(--primary), 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(var(--primary), 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
