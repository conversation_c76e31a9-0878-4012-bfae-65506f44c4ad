"use client"

import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, CircleDot, Brain, Cpu, GraduationCap } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"

const features = [
  {
    name: 'AI智能标注',
    description: '提供高精度的数据标注服务，支持图像、文本、语音等多模态数据，为机器学习模型训练提供优质数据集。',
    icon: Brain,
    details: ['图像标注', '文本标注', '语音标注', '视频标注', '3D点云标注']
  },
  {
    name: 'CPU算力租用',
    description: '灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求场景。',
    icon: Cpu,
    details: ['高性能计算', '弹性扩容', '按需付费', '24/7监控', '数据安全']
  },
  {
    name: '教育培训管理',
    description: '一站式教育培训管理平台，涵盖课程管理、学员管理、考试系统、证书颁发等完整教育生态链。',
    icon: GraduationCap,
    details: ['课程管理', '在线考试', '学员跟踪', '证书系统', '数据分析']
  }
]

const testimonials = [
  {
    content: "0dot的AI智能标注服务大大提升了我们的数据处理效率，标注精度高达99.5%，为我们的机器学习项目节省了大量时间和成本。",
    author: "张明",
    role: "AI研发总监",
    company: "智能科技有限公司",
    image: "https://picsum.photos/seed/user1/200/200"
  },
  {
    content: "使用0dot的CPU租用服务进行深度学习训练，性能稳定，价格合理。弹性扩容功能让我们能够根据项目需求灵活调整资源。",
    author: "李华",
    role: "技术架构师",
    company: "数据科学研究院",
    image: "https://picsum.photos/seed/user2/200/200"
  },
  {
    content: "教育培训管理系统功能全面，界面友好。在线考试系统特别出色，支持多种题型，防作弊功能强大，大大提升了我们的教学管理效率。",
    author: "王芳",
    role: "教务主任",
    company: "现代教育集团",
    image: "https://picsum.photos/seed/user3/200/200"
  }
]

const stats = [
  { label: '标注数据量', value: '1000万+' },
  { label: 'CPU核心数', value: '50,000+' },
  { label: '服务客户', value: '2,000+' },
  { label: '系统可用性', value: '99.9%' }
]

const solutions = [
  {
    title: 'AI数据标注',
    description: '专业的AI数据标注服务，支持图像、文本、语音等多模态数据，为机器学习提供高质量训练数据。',
    image: 'https://picsum.photos/seed/ai-annotation/800/600',
    features: ['图像识别标注', '自然语言处理', '语音识别标注', '视频内容标注']
  },
  {
    title: '云计算资源',
    description: '提供高性能CPU集群租用服务，支持科学计算、深度学习训练等高算力需求场景。',
    image: 'https://picsum.photos/seed/cloud-computing/800/600',
    features: ['弹性扩容', '按需付费', '高可用性', '安全可靠']
  },
  {
    title: '教育管理平台',
    description: '全方位的教育培训管理解决方案，涵盖课程管理、在线考试、学员跟踪等完整功能。',
    image: 'https://picsum.photos/seed/education-platform/800/600',
    features: ['课程管理', '在线考试', '学员管理', '数据分析']
  }
]

const FeatureCard = ({ feature }: { feature: typeof features[0] }) => (
  <div className="relative group h-full">
    <div className="bg-white/70 backdrop-blur-xl border border-indigo-100/50 rounded-3xl shadow-lg hover:shadow-card-hover p-8 h-full transition-all duration-500 group-hover:scale-105">
      <div className="flex items-center gap-4 mb-6">
        <div className="relative">
          <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg">
            <feature.icon className="h-8 w-8 text-white" aria-hidden="true" />
          </div>
          <div className="absolute inset-0 bg-indigo-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-slate-800 group-hover:text-indigo-600 transition-colors duration-300">
            {feature.name}
          </h3>
        </div>
      </div>
      <p className="text-slate-600 leading-relaxed mb-6 flex-grow">
        {feature.description}
      </p>
      <div className="flex flex-wrap gap-2">
        {feature.details.map((detail, index) => (
          <span
            key={detail}
            className="inline-flex items-center rounded-full bg-indigo-50 px-3 py-1.5 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-200"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {detail}
          </span>
        ))}
      </div>
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </div>
  </div>
)

const SolutionCard = ({ solution }: { solution: typeof solutions[0] }) => (
  <motion.div
    className="relative overflow-hidden rounded-2xl group"
    initial={{ opacity: 0, scale: 0.9 }}
    whileInView={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
  >
    <div className="aspect-[4/3] relative">
      <Image
        src={solution.image}
        alt={solution.title}
        fill
        className="object-cover transition-transform duration-300 group-hover:scale-110"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/0" />
    </div>
    <div className="absolute bottom-0 p-6 text-white">
      <h3 className="text-xl font-semibold mb-2">{solution.title}</h3>
      <p className="text-sm text-white/80 mb-4">{solution.description}</p>
      <div className="flex flex-wrap gap-2">
        {solution.features.map((feature) => (
          <span
            key={feature}
            className="inline-flex items-center rounded-full bg-white/20 backdrop-blur-sm px-2 py-1 text-xs font-medium text-white"
          >
            {feature}
          </span>
        ))}
      </div>
    </div>
  </motion.div>
)

const TestimonialCard = ({ testimonial }: { testimonial: typeof testimonials[0] }) => (
  <motion.figure
    className="bg-white/70 backdrop-blur-xl border border-indigo-100/50 rounded-3xl shadow-lg hover:shadow-card-hover p-8 group transition-all duration-500 hover:scale-105"
    initial={{ opacity: 0, x: -20 }}
    whileInView={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
  >
    <div className="relative">
      <div className="absolute -top-2 -left-2 text-6xl text-indigo-200 font-serif">"</div>
      <blockquote className="text-lg font-medium leading-relaxed relative z-10 mb-6">
        <p className="text-slate-700 group-hover:text-indigo-700 transition-colors duration-300">
          {testimonial.content}
        </p>
      </blockquote>
    </div>
    <figcaption className="flex items-center gap-x-4">
      <div className="relative">
        <Image
          className="h-12 w-12 rounded-full ring-2 ring-indigo-200 group-hover:ring-indigo-400 transition-all duration-300"
          src={testimonial.image}
          alt=""
          width={48}
          height={48}
        />
        <div className="absolute inset-0 bg-indigo-500/10 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>
      <div>
        <div className="font-semibold text-slate-800 group-hover:text-indigo-600 transition-colors duration-300">
          {testimonial.author}
        </div>
        <div className="text-sm text-slate-500">
          {testimonial.role}，{testimonial.company}
        </div>
      </div>
    </figcaption>
    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
  </motion.figure>
)

export default function Home() {
  return (
    <>
      {/* Hero Section */}
      <div className="relative isolate min-h-screen flex items-center">
        {/* 现代化背景层 - 蓝紫色调优化 */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-indigo-50/50 to-purple-50/30" />
          <div className="absolute inset-0 mesh-gradient opacity-30" />
          <div className="absolute inset-0 grid-bg opacity-15" />
        </div>

        {/* 浮动装饰元素 - 蓝紫色系 */}
        <div className="absolute top-20 left-20 w-72 h-72 bg-indigo-500/10 rounded-full blur-3xl animate-float opacity-70" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/8 rounded-full blur-3xl animate-float opacity-50" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-blue-500/5 via-indigo-500/8 to-purple-500/5 rounded-full blur-3xl animate-glow" />
        <div className="absolute top-10 right-10 w-64 h-64 bg-cyan-500/6 rounded-full blur-2xl animate-float opacity-60" style={{ animationDelay: '4s' }} />

        <div className="relative z-10 mx-auto max-w-7xl px-6 lg:px-8 w-full">
          <div className="mx-auto max-w-4xl text-center">
            <motion.div
              className="flex justify-center mb-12"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <div className="relative">
                <CircleDot className="h-20 w-20 text-indigo-600 animate-glow drop-shadow-lg" />
                <div className="absolute inset-0 h-20 w-20 bg-indigo-500/20 rounded-full blur-xl animate-pulse" />
                <div className="absolute -inset-4 bg-gradient-to-r from-indigo-500/20 via-purple-500/15 to-transparent rounded-full blur-2xl opacity-60" />
                <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-lg opacity-40 animate-glow" />
              </div>
            </motion.div>
            <motion.h1
              className="text-5xl font-bold tracking-tight sm:text-7xl lg:text-8xl bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
            >
              智能标注 · 算力租用 · 教育管理
            </motion.h1>
            <motion.p
              className="mt-8 text-xl leading-relaxed text-muted-foreground max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5, ease: "easeOut" }}
            >
              专业的AI数据标注服务、灵活的云计算资源租用、完整的教育培训管理解决方案。助力企业数字化转型，推动人工智能产业发展。
            </motion.p>
            <motion.div
              className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7, ease: "easeOut" }}
            >
              <Button asChild className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 text-lg h-12 rounded-xl shadow-lg hover:shadow-glow transition-all duration-300 hover:scale-105 group">
                <Link href="/products">
                  探索产品
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button className="glass px-8 py-4 text-lg h-12 bg-white/10 backdrop-blur-sm border border-indigo-200/50 hover:bg-indigo-50/50 text-indigo-700 hover:text-indigo-800 rounded-xl transition-all duration-300">
                了解更多
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Data Statistics Section */}
      <motion.div
        className="relative -mt-20 pb-32"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: true }}
      >
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-5xl bg-white/60 backdrop-blur-xl border border-indigo-100/50 rounded-3xl shadow-xl shadow-indigo-500/10 p-12">
            <div className="grid grid-cols-2 gap-12 md:grid-cols-4">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  className="text-center group"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                    {stat.value}
                  </div>
                  <div className="text-sm lg:text-base text-slate-600 font-medium">
                    {stat.label}
                  </div>
                  <div className="mt-2 h-1 w-12 bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Features Section */}
      <section className="py-32 sm:py-40 relative overflow-hidden">
        {/* 背景装饰 - 蓝紫色调 */}
        <div className="absolute inset-0 bg-gradient-to-b from-indigo-50/50 via-purple-50/30 to-slate-50" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-indigo-500/8 rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/6 rounded-full blur-3xl" />
        <div className="absolute top-1/2 right-0 w-64 h-64 bg-blue-500/5 rounded-full blur-2xl" />

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-lg font-semibold leading-7 text-indigo-600 mb-4">技术创新</h2>
            <p className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent mb-8">
              引领行业数字化转型
            </p>
            <p className="text-xl leading-relaxed text-muted-foreground">
              秉持&quot;技术引领未来，创新驱动发展&quot;的理念，我们致力于将前沿科技转化为实际应用，为客户创造价值。
            </p>
          </motion.div>
          <div className="mx-auto mt-20 max-w-2xl sm:mt-24 lg:mt-32 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-20 lg:max-w-none lg:grid-cols-3">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.name}
                  initial={{ opacity: 0, y: 40 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                >
                  <FeatureCard feature={feature} />
                </motion.div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary">行业赋能</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              定制化解决方案
            </p>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              通过跨界融合与协同创新，为多个行业提供智能化解决方案，助力传统产业升级转型。
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
            {solutions.map((solution) => (
              <SolutionCard key={solution.title} solution={solution} />
            ))}
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="py-24 sm:py-32 bg-background">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-xl text-center">
            <h2 className="text-lg font-semibold leading-8 text-primary">用户反馈</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              值得信赖的技术伙伴
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-2">
            {testimonials.map((testimonial) => (
              <TestimonialCard key={testimonial.author} testimonial={testimonial} />
            ))}
          </div>
        </div>
      </div>

      {/* Partners Section */}
      <div className="py-24 sm:py-32 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary">合作伙伴</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              值得信赖的技术生态
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-lg grid-cols-4 items-center gap-x-8 gap-y-12 sm:max-w-xl sm:grid-cols-6 sm:gap-x-10 sm:gap-y-14 lg:max-w-4xl lg:grid-cols-5">
            {[1, 2, 3, 4, 5].map((i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Image
                  className="col-span-2 max-h-12 w-full object-contain opacity-50 hover:opacity-100 transition-opacity lg:col-span-1"
                  src={`https://picsum.photos/seed/company${i}/158/48`}
                  alt={`Partner ${i}`}
                  width={158}
                  height={48}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="relative isolate mt-32 px-6 py-32 sm:mt-40 sm:py-40 lg:px-8">
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 backdrop-blur-3xl" />
        </div>
        <div className="mx-auto max-w-2xl text-center">
          <motion.h2 
            className="text-3xl font-bold tracking-tight sm:text-4xl"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            准备好开启数字化转型了吗？
          </motion.h2>
          <motion.p 
            className="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            加入数千家企业的行列，探索零点科技如何帮助您实现业务创新。
          </motion.p>
          <motion.div 
            className="mt-10 flex items-center justify-center gap-x-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Button asChild className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3 text-lg rounded-xl shadow-lg hover:shadow-glow transition-all duration-300 hover:scale-105">
              <Link href="/contact-us">预约咨询</Link>
            </Button>
            <Button className="glass px-8 py-3 text-lg bg-white/10 backdrop-blur-sm border border-indigo-200/50 hover:bg-indigo-50/50 text-indigo-700 hover:text-indigo-800 rounded-xl transition-all duration-300">
              查看案例
            </Button>
          </motion.div>
        </div>
      </div>
    </>
  )
}